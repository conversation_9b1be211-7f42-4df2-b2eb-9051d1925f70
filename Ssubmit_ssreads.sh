#!/bin/bash
#SBATCH -A grp-org-sc-mgs
#SBATCH -q jgi_normal
#SBATCH -J ssreads
#SBATCH -c 16
#SBATCH --mem=128GB
#SBATCH -t 2:00:00
#SBATCH --output=ss_%A_%a.out
#SBATCH --error=ss_%A_%a.err
#SBATCH --array=1-$(ls readsf/*.fastq.gz | wc -l)

# Install pixi environment if not already installed
if [ ! -d ".pixi" ]; then
    pixi install
fi

# Activate pixi environment
eval "$(pixi shell-hook)"
pixi activate

# Create output directory
OUTPUT_DIR="readsf_10M"
mkdir -p $OUTPUT_DIR

# Get the input file based on array task ID
INPUT_FILES=(readsf/*.fastq.gz)
INPUT_FILE="${INPUT_FILES[$SLURM_ARRAY_TASK_ID-1]}"
BASENAME=$(basename "$INPUT_FILE")
OUTPUT_FILE="$OUTPUT_DIR/$BASENAME"

# Run BBTools directly for each file
reformat.sh in="$INPUT_FILE" out="$OUTPUT_FILE" samplereadstarget=20000000 sampleseed=42 ow=t int=t