#!/usr/bin/env python3

import os
import subprocess
import argparse
import sys

def check_bbtools_installed():
    """Check if BBTools' reformat.sh is available in the PATH."""
    try:
        subprocess.run(['which', 'reformat.sh'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except subprocess.CalledProcessError:
        return False

def subsample_with_bbtools(input_file, output_file, num_pairs, seed=42):
    """
    Subsample interleaved FASTQ file using BBTools' reformat.sh

    Args:
        input_file: Path to input interleaved FASTQ.gz file
        output_file: Path to output interleaved FASTQ.gz file
        num_pairs: Number of read pairs to sample
        seed: Random seed for reproducibility
    """
    # Build the reformat.sh command
    cmd = [
        'reformat.sh',
        f'in={input_file}',
        f'out={output_file}',
        f'samplereadstarget={num_pairs*2}',  # *2 because BBTools counts individual reads, not pairs
        f'sampleseed={seed}',
        'ow=t',  # Overwrite existing files
        'int=t'   # Specify input is interleaved
    ]

    print(f"Running: {' '.join(cmd)}")

    try:
        # Run the command
        result = subprocess.run(
            cmd,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )

        # Print BBTools output
        print(result.stderr)

        print(f"Successfully subsampled to {output_file}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running BBTools: {e}")
        print(f"STDERR: {e.stderr}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Randomly subsample read pairs from interleaved FASTQ files using BBTools")
    parser.add_argument("input_file", help="Input interleaved FASTQ.gz file")
    parser.add_argument("output_file", help="Output subsampled FASTQ.gz file")
    parser.add_argument("--num_pairs", "-n", type=int, required=True,
                        help="Number of read pairs to sample")
    parser.add_argument("--seed", "-s", type=int, default=42,
                        help="Random seed for reproducibility (default: 42)")

    args = parser.parse_args()

    # Check if BBTools is installed
    if not check_bbtools_installed():
        print("Error: BBTools' reformat.sh not found in PATH. Please install BBTools or add it to your PATH.")
        sys.exit(1)

    # Create output directory if it doesn't exist
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Process the single file
    success = subsample_with_bbtools(args.input_file, args.output_file, args.num_pairs, args.seed)

    if success:
        print(f"Successfully subsampled {args.input_file} to {args.output_file}")
        sys.exit(0)
    else:
        print(f"Failed to subsample {args.input_file}")
        sys.exit(1)

if __name__ == "__main__":
    main()
