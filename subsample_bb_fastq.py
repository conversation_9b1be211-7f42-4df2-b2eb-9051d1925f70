#!/usr/bin/env python3

import os
import subprocess
import argparse
import glob
import sys

def check_bbtools_installed():
    """Check if BBTools' reformat.sh is available in the PATH."""
    try:
        subprocess.run(['which', 'reformat.sh'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except subprocess.CalledProcessError:
        return False

def subsample_with_bbtools(input_file, output_file, num_pairs, seed=42):
    """
    Subsample interleaved FASTQ file using BBTools' reformat.sh
    
    Args:
        input_file: Path to input interleaved FASTQ.gz file
        output_file: Path to output interleaved FASTQ.gz file
        num_pairs: Number of read pairs to sample
        seed: Random seed for reproducibility
    """
    # Build the reformat.sh command
    cmd = [
        'reformat.sh',
        f'in={input_file}',
        f'out={output_file}',
        f'samplereadstarget={num_pairs*2}',  # *2 because BBTools counts individual reads, not pairs
        'sampleseed=42',
        'ow=t',  # Overwrite existing files
        'int=t'   # Specify input is interleaved
    ]
    
    print(f"Running: {' '.join(cmd)}")
    
    try:
        # Run the command
        result = subprocess.run(
            cmd, 
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        # Print BBTools output
        print(result.stderr)
        
        print(f"Successfully subsampled to {output_file}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running BBTools: {e}")
        print(f"STDERR: {e.stderr}")
        return False

def process_directory(input_dir, output_dir, num_pairs, seed=42):
    """Process all interleaved FASTQ files in the input directory using BBTools."""
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all .fastq.gz files in the input directory
    fastq_files = glob.glob(os.path.join(input_dir, "*.fastq.gz"))
    
    if not fastq_files:
        print(f"No FASTQ.gz files found in {input_dir}")
        return
    
    print(f"Found {len(fastq_files)} FASTQ.gz files to process")
    
    success_count = 0
    for input_file in fastq_files:
        filename = os.path.basename(input_file)
        output_file = os.path.join(output_dir, filename)
        
        print(f"\nProcessing {filename}...")
        if subsample_with_bbtools(input_file, output_file, num_pairs, seed):
            success_count += 1
    
    print(f"\nCompleted processing. Successfully subsampled {success_count} of {len(fastq_files)} files.")

def main():
    parser = argparse.ArgumentParser(description="Randomly subsample read pairs from interleaved FASTQ files using BBTools")
    parser.add_argument("input_dir", help="Directory containing interleaved FASTQ.gz files")
    parser.add_argument("output_dir", help="Directory to save subsampled FASTQ.gz files")
    parser.add_argument("--num_pairs", "-n", type=int, required=True, 
                        help="Number of read pairs to sample from each file")
    parser.add_argument("--seed", "-s", type=int, default=42,
                        help="Random seed for reproducibility (default: 42)")
    
    args = parser.parse_args()
    
    # Check if BBTools is installed
    if not check_bbtools_installed():
        print("Error: BBTools' reformat.sh not found in PATH. Please install BBTools or add it to your PATH.")
        sys.exit(1)
    
    # Process the directory
    process_directory(args.input_dir, args.output_dir, args.num_pairs, args.seed)

if __name__ == "__main__":
    main()
